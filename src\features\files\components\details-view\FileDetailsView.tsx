import { PDFViewerPage } from "../viewer";
import { PatternInfoComponent } from "./PatternInfo";
import { DeleteFileButton } from "./DeleteButton";
import { clerkClient } from "@clerk/nextjs/server";
import { getPatternDetails } from "~/server/queries";
import type { PatternAnalysis } from "~/lib/api/external/ai-file-info/generate-pdf-analysis";

export async function FileDetailsView({ fileId }: { fileId: number }) {
  const file = await getPatternDetails(fileId);
  const { users } = await clerkClient();
  const uploaderInfo = await users.getUser(file.userId);

  // Create the file object with the correct structure for PDFViewerPage
  const pdfFile = {
    url: file.url,
    thumbnailUrl: file.thumbnailUrl,
  };

  return (
    <div className="flex h-full w-full min-w-0">
      <div className="flex flex-1 items-start justify-start overflow-x-auto p-4 pr-0">
        <PDFViewerPage file={pdfFile} />
      </div>
      <div className="flex w-96 flex-shrink-0 flex-col border-l">
        <PatternInfoComponent
          patternData={file as unknown as PatternAnalysis}
        />
        <div className="flex flex-col p-2">
          <span>Uploaded By:</span>
          <span>
            {uploaderInfo.firstName} {uploaderInfo.lastName}
          </span>
        </div>
        <div className="flex flex-col p-2">
          <span>Created On:</span>
          <span>{new Date(file.createdAt).toLocaleDateString()}</span>
        </div>
        <div className="flex flex-col p-2">
          <DeleteFileButton fileId={fileId} />
        </div>
      </div>
    </div>
  );
}
