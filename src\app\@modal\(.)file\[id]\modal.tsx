"use client";

import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { createPortal } from "react-dom";

export function Modal({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const dialogRef = useRef<HTMLDialogElement | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !dialogRef.current?.open) {
      dialogRef.current?.showModal();
    }
  }, [mounted]);

  function onDismiss() {
    router.back();
  }

  if (!mounted) {
    return null;
  }

  const modalRoot = document.getElementById("modal-root");
  if (!modalRoot) {
    console.error("modal-root element not found");
    return null;
  }

  return createPortal(
    <dialog
      ref={dialogRef}
      className="fixed inset-0 z-50 h-screen w-screen bg-background text-foreground"
      onClose={onDismiss}
    >
      {children}
    </dialog>,
    modalRoot,
  );
}
