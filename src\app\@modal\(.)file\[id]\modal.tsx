"use client";

import { useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { createPortal } from "react-dom";

export function Modal({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const dialogRef = useRef<HTMLDialogElement | null>(null);

  useEffect(() => {
    if (!dialogRef.current?.open) {
      dialogRef.current?.showModal();
    }
  }, []);

  function onDismiss() {
    router.back();
  }

  return createPortal(
    <div className="fixed inset-0 z-50">
      <div className="fixed inset-0 bg-black/50" onClick={onDismiss} />
      <dialog
        ref={dialogRef}
        className="fixed inset-0 z-50 h-screen w-screen bg-background text-foreground"
        onClose={onDismiss}
      >
        {children}
      </dialog>
    </div>,
    document.getElementById("modal-root")!,
  );
}
