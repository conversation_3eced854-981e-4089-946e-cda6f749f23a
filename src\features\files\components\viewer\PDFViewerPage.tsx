"use client";

/**
 * PDFViewerPage - Top-level component for PDF viewing experience
 *
 * This component orchestrates the PDF viewing experience by:
 * 1. Managing page navigation state
 * 2. Coordinating between the display frame and navigation controls
 * 3. Handling mouse events for showing/hiding navigation controls
 */

import React, { useEffect } from "react";
import "~/lib/pdfjs-config"; // Required for PDF.js initialization
import { PDFPageControls } from "./PDFPageControls";
import PDFDisplayFrame from "./PDFDisplayFrame";
import { usePdfNavigation } from "../../hooks";
import { prefetchPdfDocument } from "../../hooks";
import { useQueryClient } from "@tanstack/react-query";

interface PDFViewerContainerProps {
  /** Handler for showing navigation controls on mouse enter */
  handleMouseEnter: () => void;
  /** Handler for hiding navigation controls on mouse leave */
  handleMouseLeave: () => void;
  /** Child components (PDF display and controls) */
  children: React.ReactNode;
}

/**
 * Container component that provides layout and mouse event handling
 * for the PDF viewer and its controls
 */
const PDFViewerContainer = ({
  handleMouseEnter,
  handleMouseLeave,
  children,
}: PDFViewerContainerProps) => {
  return (
    <div
      className="relative w-full max-w-full overflow-hidden"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
    </div>
  );
};

/**
 * Generic file type that works with both BasicFile and PatternDetails models
 */
type FileWithUrl = {
  /** URL to the PDF file */
  url: string;
  /** Optional URL to a thumbnail image of the PDF */
  thumbnailUrl?: string | null;
};

/**
 * Main PDF viewer page component that coordinates the viewing experience
 */
export const PDFViewerPage: React.FC<{ file: FileWithUrl }> = ({ file }) => {
  // Extract file properties
  const { url: src, thumbnailUrl } = file;
  const queryClient = useQueryClient();

  // Use the navigation hook to manage page state and controls
  const {
    pageNumber,
    numPages,
    navIsVisible,
    onDocLoadSuccess,
    previousPage,
    nextPage,
    handleMouseEnter,
    handleMouseLeave,
  } = usePdfNavigation();

  // Prefetch the PDF document when the component mounts
  useEffect(() => {
    if (src) {
      void prefetchPdfDocument(queryClient, src);
    }
  }, [src, queryClient]);

  return (
    <PDFViewerContainer {...{ handleMouseEnter, handleMouseLeave }}>
      <PDFDisplayFrame
        {...{
          file: src,
          currentPage: pageNumber,
          thumbnailUrl: thumbnailUrl,
          onDocLoadSuccess: onDocLoadSuccess,
        }}
      />

      {numPages && (
        <PDFPageControls
          {...{
            isVisible: navIsVisible,
            currentPage: pageNumber,
            totalPages: numPages,
            onPrevious: previousPage,
            onNext: nextPage,
          }}
        />
      )}
    </PDFViewerContainer>
  );
};
