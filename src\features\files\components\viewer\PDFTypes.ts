/**
 * Shared type definitions and constants for PDF viewer components
 */

/** Props for the PDFDocumentRenderer component */
export interface PDFDocumentRendererProps {
  /** URL or data for the PDF file to display */
  file: string;

  /** Optional alternative PDF URL */
  pdfUrl?: string;

  /** Current page number to display */
  currentPage: number;

  /** Scale factor for the PDF page */
  scale: number;

  /** Callback when the document is successfully loaded */
  onDocLoadSuccess: (numPages: number) => void;

  /** Callback when the PDF page is fully rendered */
  onPDFLoaded: () => void;
}

/**
 * Size options for the PDF viewer
 *
 * - "thumbnail": Small preview size used in gallery and card views
 * - "full": Larger size used in the detailed viewer
 */
export type PDFViewerSize = "thumbnail" | "full";

/**
 * Dimensions for different PDF viewer sizes
 *
 * These dimensions maintain the standard 8.5x11 aspect ratio (letter size)
 * and are used as the base dimensions for responsive calculations.
 *
 * For thumbnails, we use fixed dimensions.
 * For full view, the width is used as a maximum, and height is calculated
 * based on the container width to maintain aspect ratio.
 */
export const PDF_DIMENSIONS = {
  thumbnail: { width: 192, height: 256 }, // 3:4 ratio for thumbnails
  full: { width: 650, height: 866 }, // Standard letter size ratio (appropriate for viewing)
} as const;
