"use client";

/**
 * PDFDisplayFrame - Container component for PDF display
 *
 * This component handles the visual presentation of a PDF by:
 * 1. Showing a thumbnail preview while the PDF is loading
 * 2. Displaying a loading indicator
 * 3. Providing a container for the actual PDF renderer
 */

import React from "react";
import dynamic from "next/dynamic";
import { Loader2 } from "lucide-react";
import Image from "next/image";

import type { PDFDocumentRendererProps } from "./PDFTypes";
import type { PDFViewerSize } from "./PDFTypes";
import { PDF_DIMENSIONS } from "./PDFTypes";

/**
 * Displays a thumbnail preview while the PDF is loading
 */
const ThumbnailPreview: React.FC<{
  url: string;
  isLoaded: boolean;
}> = ({ url, isLoaded }) => (
  <div
    className="absolute inset-0 flex w-full items-start justify-start transition-all"
    style={{
      zIndex: isLoaded ? 0 : 10,
      transitionDuration: "700ms",
      transitionTimingFunction: "cubic-bezier(0.4, 0, 0.2, 1)",
      opacity: isLoaded ? 0 : 1,
      filter: isLoaded ? "none" : "blur(8px)",
    }}
  >
    <Image
      src={url}
      alt="PDF preview"
      width={650}
      height={866}
      className="h-[866px] w-auto object-cover object-left-top"
      priority
      quality={95}
      style={{ minWidth: "650px" }}
    />
  </div>
);

const LoadingOverlay: React.FC<{ isLoaded: boolean }> = ({ isLoaded }) => {
  const { height } = PDF_DIMENSIONS.full;

  return (
    <div
      className="absolute inset-0 z-20 flex w-full items-center justify-center transition-all"
      style={{
        height: `${height}px`,
        minWidth: "100%",
        transitionDuration: "700ms",
        transitionTimingFunction: "cubic-bezier(0.4, 0, 0.2, 1)",
        opacity: isLoaded ? 0 : 1,
        pointerEvents: isLoaded ? "none" : "auto",
      }}
    >
      <div className="bg-background/40 flex items-center justify-center rounded-full p-2 shadow-sm">
        <Loader2 className="h-7 w-7 animate-spin text-primary" />
      </div>
    </div>
  );
};

/**
 * Dynamically import the PDF renderer component to avoid SSR issues
 */
const PDFDocumentRenderer = dynamic<PDFDocumentRendererProps>(
  () => import("./PDFDocumentRenderer").then((mod) => mod.default),
  {
    ssr: false,
    loading: () => <span className="sr-only">Loading PDF viewer...</span>,
  },
);

interface PDFDisplayFrameProps {
  /** URL or data for the PDF file to display */
  file: string;

  /** Current page number to display */
  currentPage: number;

  /** Size preset for the PDF viewer ("thumbnail" or "full") */
  size?: PDFViewerSize;

  /** Optional URL to a thumbnail image to show while the PDF loads */
  thumbnailUrl?: string | null;

  /** Callback when the document is successfully loaded */
  onDocLoadSuccess?: (args: { numPages: number }) => void;
}

/**
 * Component that handles the display and loading states of a PDF
 */
const PDFDisplayFrame: React.FC<PDFDisplayFrameProps> = ({
  file,
  currentPage,
  size = "full",
  thumbnailUrl: providedThumbnailUrl,
  onDocLoadSuccess,
}) => {
  const validSize: PDFViewerSize = size === "thumbnail" ? "thumbnail" : "full";
  const dimensions = PDF_DIMENSIONS[validSize];
  const thumbnailUrl = providedThumbnailUrl;
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [isFullyRendered, setIsFullyRendered] = React.useState(false);

  const handleDocLoadSuccess = (numPages: number) => {
    if (onDocLoadSuccess) {
      onDocLoadSuccess({ numPages });
    }
  };

  const handlePdfLoaded = () => {
    console.log("PDF loaded, starting transition");
    // First mark as loaded (PDF is rendered but may not be fully painted)
    setIsLoaded(true);

    // Then after a short delay, mark as fully rendered
    // This keeps the thumbnail visible slightly longer to prevent white flash
    setTimeout(() => {
      console.log("PDF fully rendered");
      setIsFullyRendered(true);
    }, 200);
  };

  console.log("PDFDisplayFrame", file);

  if (validSize === "thumbnail") {
    return (
      <div className="w-48 overflow-hidden rounded-lg border bg-card shadow-sm">
        {thumbnailUrl ? (
          <Image
            src={thumbnailUrl}
            alt="PDF preview"
            width={dimensions.width}
            height={dimensions.height}
            className="h-auto w-full object-cover"
          />
        ) : (
          <div
            style={{
              width: dimensions.width,
              height: dimensions.height,
              backgroundColor: "var(--color-card)",
            }}
            className="flex h-full w-full items-center justify-center"
          >
            <Loader2 className="text-primary/50 h-6 w-6 animate-spin" />
          </div>
        )}
      </div>
    );
  }

  const { height } = PDF_DIMENSIONS.full;

  return (
    <div className="relative">
      {/* Let the PDF render at its natural size */}
      {thumbnailUrl && (
        <ThumbnailPreview url={thumbnailUrl} isLoaded={isFullyRendered} />
      )}

      <LoadingOverlay isLoaded={isLoaded} />

      <div
        className="transition-all"
        style={{
          position: "relative",
          zIndex: isFullyRendered ? 5 : 0,
          transitionDuration: "700ms",
          transitionTimingFunction: "cubic-bezier(0.4, 0, 0.2, 1)",
          opacity: isLoaded ? 1 : 0,
          filter: isLoaded ? "none" : "blur(8px)",
        }}
      >
        <PDFDocumentRenderer
          file={file}
          currentPage={currentPage}
          height={height}
          onDocLoadSuccess={handleDocLoadSuccess}
          onPDFLoaded={handlePdfLoaded}
        />
      </div>
    </div>
  );
};

export default PDFDisplayFrame;
