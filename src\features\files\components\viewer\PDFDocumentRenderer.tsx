"use client";

/**
 * Core component that renders the actual PDF document using react-pdf
 */

import { useState, useEffect } from "react";
import { Document, Page } from "react-pdf";
import { loadWorker } from "~/lib/pdfjs-config";
import { cn } from "~/lib/utils";

interface PDFDocumentRendererProps {
  file: string;
  pdfUrl?: string;
  onDocLoadSuccess: (numPages: number) => void;
  onPDFLoaded: () => void;
  currentPage: number;
  scale: number;
}

export default function PDFDocumentRenderer({
  file,
  pdfUrl,
  onDocLoadSuccess,
  onPDFLoaded,
  currentPage,
  scale,
}: PDFDocumentRendererProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [pageWidth, setPageWidth] = useState<number | undefined>(undefined);

  useEffect(() => {
    // Load the PDF.js worker
    try {
      loadWorker();
    } catch (promise) {
      // This is expected - the worker is loading
      console.log("PDF.js worker is loading...");
    }
  }, []);

  const handleLoadSuccess = async (pdf: any) => {
    const numPages = pdf.numPages;
    onDocLoadSuccess(numPages);

    // Get the first page to determine the actual width
    try {
      const page = await pdf.getPage(1);
      const viewport = page.getViewport({ scale });
      console.log(
        "PDF viewport width:",
        viewport.width,
        "height:",
        viewport.height,
        "scale:",
        scale,
        "pageWidth state:",
        pageWidth,
      );
      setPageWidth(viewport.width);
    } catch (error) {
      console.error("Error getting page dimensions:", error);
    }
  };

  const handleRenderSuccess = () => {
    setIsLoading(false);
    onPDFLoaded();
  };

  const documentSource = pdfUrl ?? file;

  return (
    <div className="relative inline-block">
      <Document
        file={documentSource}
        onLoadSuccess={handleLoadSuccess}
        loading={
          <div className="flex h-full w-full items-center justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
          </div>
        }
        error={
          <div className="flex h-full w-full items-center justify-center text-destructive">
            Failed to load PDF
          </div>
        }
      >
        <Page
          pageNumber={currentPage}
          scale={scale}
          width={pageWidth}
          onRenderSuccess={handleRenderSuccess}
          loading={
            <div className="flex h-full w-full items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            </div>
          }
          error={
            <div className="flex h-full w-full items-center justify-center text-destructive">
              Failed to render page
            </div>
          }
          className={cn(
            "transition-opacity duration-300",
            isLoading ? "opacity-0" : "opacity-100",
          )}
        />
      </Document>
    </div>
  );
}
