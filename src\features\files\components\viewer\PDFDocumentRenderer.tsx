"use client";

/**
 * Core component that renders the actual PDF document using react-pdf
 */

import { useState, useEffect } from "react";
import { Document, Page } from "react-pdf";
import { loadWorker } from "~/lib/pdfjs-config";
import { cn } from "~/lib/utils";

interface PDFDocumentRendererProps {
  file: string;
  pdfUrl?: string;
  onDocLoadSuccess: (numPages: number) => void;
  onPDFLoaded: () => void;
  currentPage: number;
  scale: number;
}

export default function PDFDocumentRenderer({
  file,
  pdfUrl,
  onDocLoadSuccess,
  onPDFLoaded,
  currentPage,
  scale,
}: PDFDocumentRendererProps) {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load the PDF.js worker
    try {
      loadWorker();
    } catch (promise) {
      // This is expected - the worker is loading
      console.log("PDF.js worker is loading...");
    }
  }, []);

  const handleLoadSuccess = ({ numPages }: { numPages: number }) => {
    onDocLoadSuccess(numPages);
  };

  const handleRenderSuccess = () => {
    setIsLoading(false);
    onPDFLoaded();
  };

  const documentSource = pdfUrl ?? file;

  return (
    <div className="relative h-full w-full">
      <Document
        file={documentSource}
        onLoadSuccess={handleLoadSuccess}
        loading={
          <div className="flex h-full w-full items-center justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
          </div>
        }
        error={
          <div className="flex h-full w-full items-center justify-center text-destructive">
            Failed to load PDF
          </div>
        }
      >
        <Page
          pageNumber={currentPage}
          scale={scale}
          onRenderSuccess={handleRenderSuccess}
          loading={
            <div className="flex h-full w-full items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            </div>
          }
          error={
            <div className="flex h-full w-full items-center justify-center text-destructive">
              Failed to render page
            </div>
          }
          className={cn(
            "transition-opacity duration-300",
            isLoading ? "opacity-0" : "opacity-100",
          )}
        />
      </Document>
    </div>
  );
}
